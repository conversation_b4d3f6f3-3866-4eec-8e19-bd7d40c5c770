{"name": "gemini-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@blocknote/core": "^0.35.0", "@blocknote/react": "^0.35.0", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/postcss": "^4.1.11", "@tiptap/extension-link": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1"}, "devDependencies": {"@types/node": "^22.14.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^6.2.0"}}