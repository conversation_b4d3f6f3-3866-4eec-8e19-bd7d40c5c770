import React from 'react';
import { HashRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { BlogProvider } from './context/BlogContext';
import Header from './components/Header';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import PostPage from './pages/PostPage';
import AdminPage from './pages/AdminPage';
import CategoryPage from './pages/CategoryPage';
import TagPage from './pages/TagPage';
import PostsPage from './pages/PostsPage';
import MediaPage from './pages/MediaPage';
import CategoriesPage from './pages/CategoriesPage';
import TagsPage from './pages/TagsPage';
import SettingsPage from './pages/SettingsPage';

const NotFound: React.FC = () => (
    <div className="text-center py-20">
        <h1 className="text-6xl font-bold font-display text-primary">404</h1>
        <h2 className="mt-4 text-3xl font-bold font-display text-neutral-800">Page Not Found</h2>
        <p className="mt-4 text-neutral-600">Sorry, we couldn’t find the page you’re looking for.</p>
    </div>
)

const AppContent: React.FC = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div className="flex flex-col min-h-screen bg-neutral-50">
      {!isAdminRoute && <Header />}
      <main className="flex-grow">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/post/:slug" element={<PostPage />} />
          <Route path="/category/:categoryName" element={<CategoryPage />} />
          <Route path="/tag/:tagName" element={<TagPage />} />
          <Route path="/admin" element={<AdminPage />} />
          <Route path="/admin/posts" element={<PostsPage />} />
          <Route path="/admin/media" element={<MediaPage />} />
          <Route path="/admin/categories" element={<CategoriesPage />} />
          <Route path="/admin/tags" element={<TagsPage />} />
          <Route path="/admin/settings" element={<SettingsPage />} />
          <Route path="/404" element={<NotFound />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </main>
      {!isAdminRoute && <Footer />}
    </div>
  );
}

const App: React.FC = () => {
  return (
    <BlogProvider>
      <HashRouter>
        <AppContent />
      </HashRouter>
    </BlogProvider>
  );
};

export default App;