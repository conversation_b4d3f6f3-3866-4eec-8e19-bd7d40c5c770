
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { Post } from '../types';
import { supabase } from '../supabaseClient';
import { Session } from '@supabase/supabase-js';

interface BlogContextType {
  posts: Post[];
  isLoggedIn: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  addPost: (post: Omit<Post, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'author' | 'authorImageUrl'>) => Promise<void>;
  updatePost: (post: Post) => Promise<void>;
  deletePost: (id: string) => Promise<void>;
  getPostBySlug: (slug: string) => Post | undefined;
  getCategories: () => string[];
  getTags: () => string[];
}

const BlogContext = createContext<BlogContextType | undefined>(undefined);

export const BlogProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      const { data, error } = await supabase.from('posts').select('*');
      if (error) {
        console.error('Error fetching posts:', error);
      } else {
        setPosts(data as Post[]);
      }
    };

    fetchPosts();

    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  const isLoggedIn = !!session;

  const login = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return !error;
  };

  const logout = async () => {
    await supabase.auth.signOut();
  };

  const addPost = async (postData: Omit<Post, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'author' | 'authorImageUrl'>) => {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      console.error('User is not authenticated.');
      return;
    }

    const newPost = {
      ...postData,
      author: user.email ?? 'Anonymous',
      authorImageUrl: user.user_metadata?.avatar_url || '',
    };

    const { data, error } = await supabase.from('posts').insert([newPost]).select();
    if (error) {
      console.error('Error adding post:', error);
      throw new Error(error.message);
    } 
    
    if (data) {
      setPosts((prevPosts: Post[]) => [...prevPosts, data[0] as Post]);
    }
  };

  const updatePost = async (updatedPost: Post) => {
    const { data, error } = await supabase.from('posts').update(updatedPost).eq('id', updatedPost.id).select();
    if (error) {
      console.error('Error updating post:', error);
    } else if (data) {
      setPosts((prevPosts: Post[]) =>
        prevPosts.map((post: Post) => (post.id === updatedPost.id ? (data[0] as Post) : post))
      );
    }
  };

  const deletePost = async (id: string) => {
    const { error } = await supabase.from('posts').delete().eq('id', id);
    if (error) {
      console.error('Error deleting post:', error);
    } else {
      setPosts((prevPosts: Post[]) => prevPosts.filter((post: Post) => post.id !== id));
    }
  };

  const getPostBySlug = (slug: string) => {
    return posts.find((post: Post) => post.slug === slug);
  };

  const getCategories = () => {
    const categories = new Set(posts.map((post: Post) => post.category));
    return Array.from(categories);
  };

  const getTags = () => {
    const tags = new Set(posts.flatMap((post: Post) => post.tags));
    return Array.from(tags);
  };

  const value = {
    posts,
    isLoggedIn,
    login,
    logout,
    addPost,
    updatePost,
    deletePost,
    getPostBySlug,
    getCategories,
    getTags,
  };

  return <BlogContext.Provider value={value}>{children}</BlogContext.Provider>;
};

export const useBlog = (): BlogContextType => {
  const context = useContext(BlogContext);
  if (context === undefined) {
    throw new Error('useBlog must be used within a BlogProvider');
  }
  return context;
};
