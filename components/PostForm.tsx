import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { useBlog } from '../context/BlogContext';
import { BlockNoteView, useBlockNote } from "@blocknote/react";
import "@blocknote/react/style.css";

interface PostFormProps {
  postToEdit?: Post | null;
  onClose: () => void;
}

const slugify = (text: string) =>
  text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/[^\w\-]+/g, '') // Remove all non-word chars
    .replace(/\-\-+/g, '-'); // Replace multiple - with single -

const PostForm: React.FC<PostFormProps> = ({ postToEdit, onClose }) => {
  const { addPost, updatePost } = useBlog();
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    imageUrl: '',
    category: '',
    tags: '',
    status: 'draft' as 'draft' | 'published',
    seoTitle: '',
    seoDescription: '',
  });

  const isEditing = Boolean(postToEdit);
  
  const baseInputClass = "block w-full rounded-md border-neutral-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm";

  const editor = useBlockNote({
    initialContent: postToEdit ? JSON.parse(postToEdit.content) : undefined,
    onEditorContentChange: (editor) => {
      handleContentChange(JSON.stringify(editor.topLevelBlocks, null, 2));
    }
  });


  useEffect(() => {
    if (postToEdit) {
      setFormData({
        title: postToEdit.title,
        slug: postToEdit.slug,
        content: postToEdit.content,
        imageUrl: postToEdit.imageUrl,
        category: postToEdit.category,
        tags: postToEdit.tags.join(', '),
        status: postToEdit.status,
        seoTitle: postToEdit.seoTitle,
        seoDescription: postToEdit.seoDescription,
      });
    } else {
      // Reset form when creating a new post
      setFormData({
        title: '',
        slug: '',
        content: '',
        imageUrl: '',
        category: '',
        tags: '',
        status: 'draft',
        seoTitle: '',
        seoDescription: '',
      });
    }
  }, [postToEdit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    if (name === 'title') {
      setFormData(prev => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleContentChange = (newContent: string) => {
    setFormData(prev => ({ ...prev, content: newContent }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const postData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
    };

    try {
      if (isEditing && postToEdit) {
        await updatePost({ ...postToEdit, ...postData });
      } else {
        await addPost(postData);
      }
      onClose();
    } catch (error) {
      console.error('Failed to submit post:', error);
      alert(`Error: ${error.message}`);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex justify-center items-start pt-12 pb-8 px-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl transform transition-all">
        <form onSubmit={handleSubmit}>
          <div className="p-8">
            <h2 className="text-2xl font-bold font-display text-neutral-900 mb-6">{isEditing ? 'Edit Post' : 'Create New Post'}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="md:col-span-2 space-y-6">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-neutral-700 mb-1">Title</label>
                  <input type="text" name="title" id="title" value={formData.title} onChange={handleChange} className={baseInputClass} required />
                </div>
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-neutral-700 mb-1">URL Slug</label>
                  <input type="text" name="slug" id="slug" value={formData.slug} onChange={handleChange} className={`${baseInputClass} bg-neutral-100`} required />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">Content</label>
                  <div className="mt-2">
                    <BlockNoteView editor={editor} theme={"light"} />
                  </div>
                </div>
              </div>
              {/* Sidebar */}
              <div className="md:col-span-1 space-y-6 bg-neutral-50 p-6 rounded-lg">
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-neutral-700 mb-1">Status</label>
                  <select id="status" name="status" value={formData.status} onChange={handleChange} className={baseInputClass}>
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-neutral-700 mb-1">Category</label>
                  <input type="text" name="category" id="category" value={formData.category} onChange={handleChange} className={baseInputClass} required />
                </div>
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-neutral-700 mb-1">Tags (comma-separated)</label>

                  <input type="text" name="tags" id="tags" value={formData.tags} onChange={handleChange} className={baseInputClass} />
                </div>
                <div>
                  <label htmlFor="imageUrl" className="block text-sm font-medium text-neutral-700 mb-1">Featured Image URL</label>
                  <input type="url" name="imageUrl" id="imageUrl" value={formData.imageUrl} onChange={handleChange} className={baseInputClass} placeholder="https://example.com/image.jpg" required/>
                </div>
              </div>
            </div>
             {/* SEO Section */}
            <div className="mt-8 border-t border-neutral-200 pt-6 space-y-6">
                 <h3 className="text-lg font-medium leading-6 text-neutral-900 font-display">SEO Settings</h3>
                 <div>
                  <label htmlFor="seoTitle" className="block text-sm font-medium text-neutral-700 mb-1">SEO Title</label>
                  <input type="text" name="seoTitle" id="seoTitle" value={formData.seoTitle} onChange={handleChange} className={baseInputClass} />
                </div>
                <div>
                  <label htmlFor="seoDescription" className="block text-sm font-medium text-neutral-700 mb-1">SEO Description</label>
                  <textarea name="seoDescription" id="seoDescription" value={formData.seoDescription} onChange={handleChange} rows={3} className={baseInputClass}></textarea>
                </div>
            </div>
          </div>
          <div className="bg-neutral-50 px-8 py-4 flex justify-end items-center space-x-3 rounded-b-lg border-t border-neutral-200">
            <button type="button" onClick={onClose} className="rounded-md border border-neutral-300 bg-white py-2 px-4 text-sm font-medium text-neutral-700 shadow-sm hover:bg-neutral-50 transition-colors">Cancel</button>
            <button type="submit" className="inline-flex justify-center rounded-md border border-transparent bg-primary py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-primary-dark transition-colors">{isEditing ? 'Save Changes' : 'Publish Post'}</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PostForm;