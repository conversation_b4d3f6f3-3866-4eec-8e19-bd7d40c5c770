import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { HomeIcon, PostIcon, MediaIcon, CategoryIcon, TagIcon, SettingsIcon } from '../components/Icons';

const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: HomeIcon },
    { name: 'Posts', href: '/admin/posts', icon: PostIcon },
    { name: 'Media', href: '/admin/media', icon: MediaIcon },
    { name: 'Categories', href: '/admin/categories', icon: CategoryIcon },
    { name: 'Tags', href: '/admin/tags', icon: TagIcon },
    { name: 'Settings', href: '/admin/settings', icon: SettingsIcon },
  ];

  return (
    <div className="flex h-screen bg-neutral-100">
      <aside className="w-64 bg-neutral-800 text-white flex flex-col">
        <div className="h-16 flex items-center justify-center text-2xl font-bold font-display">
          Gemini Blog
        </div>
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${
                location.pathname === item.href
                  ? 'bg-primary text-white'
                  : 'text-neutral-300 hover:bg-neutral-700 hover:text-white'
              }`}
            >
              <item.icon className="h-6 w-6 mr-3" />
              {item.name}
            </Link>
          ))}
        </nav>
      </aside>
      <main className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-y-auto p-8">
          {children}
        </div>
      </main>
    </div>
  );
};

export default AdminLayout;