import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useBlog } from '../context/BlogContext';

const Header: React.FC = () => {
  const { isLoggedIn, logout } = useBlog();

  const navLinkClass = ({ isActive }: { isActive: boolean }) =>
    `relative text-sm font-medium transition-colors text-neutral-600 hover:text-primary-dark ${
      isActive ? 'text-primary' : ''
    } after:absolute after:bottom-[-4px] after:left-0 after:h-[2px] after:w-full after:bg-primary after:transform after:scale-x-0 after:transition-transform after:duration-300 ${
      isActive ? 'after:scale-x-100' : ''
    }`;

  return (
    <header className="bg-white/70 backdrop-blur-lg sticky top-0 z-40 border-b border-neutral-200">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex-shrink-0">
            <Link to="/" className="text-2xl font-display font-bold text-neutral-800 hover:text-primary-dark transition-colors">
              Gemini Blog
            </Link>
          </div>
          <nav className="flex items-center space-x-6">
            <NavLink to="/" className={navLinkClass}>
              Home
            </NavLink>
            <NavLink to="/admin" className={navLinkClass}>
              Admin
            </NavLink>
            {isLoggedIn && (
               <button onClick={logout} className="text-sm font-medium text-neutral-600 hover:text-primary-dark transition-colors">
                 Logout
               </button>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;