import React from 'react';
import { Link } from 'react-router-dom';
import { Post } from '../types';

interface PostCardProps {
  post: Post;
}

const PostCard: React.FC<PostCardProps> = ({ post }) => {
  const excerpt = post.content.split('\n')[0].substring(0, 100) + '...';

  return (
    <article className="group flex flex-col overflow-hidden rounded-lg border border-neutral-200 bg-white shadow-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <Link to={`/post/${post.slug}`} className="block overflow-hidden">
        <img
          src={post.imageUrl}
          alt={post.title}
          className="h-48 w-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </Link>
      <div className="flex flex-1 flex-col justify-between p-5">
        <div>
          <div className="mb-3">
            <Link
              to={`/category/${encodeURIComponent(post.category)}`}
              className="text-xs font-semibold uppercase tracking-wider text-primary bg-primary/10 px-2 py-1 rounded-md hover:bg-primary/20 transition-colors"
            >
              {post.category}
            </Link>
          </div>
          <Link to={`/post/${post.slug}`} className="block">
            <h3 className="text-lg font-bold font-display text-neutral-900 group-hover:text-primary-dark">{post.title}</h3>
            <p className="mt-2 text-sm text-neutral-600 leading-relaxed">{excerpt}</p>
          </Link>
        </div>
        <div className="mt-5 flex items-center">
          <div className="flex-shrink-0">
              <img className="h-10 w-10 rounded-full object-cover" src={post.authorImageUrl} alt={post.author} />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-neutral-900">{post.author}</p>
            <div className="flex space-x-1 text-xs text-neutral-500">
              <time dateTime={post.createdAt}>{new Date(post.createdAt).toLocaleDateString()}</time>
              <span aria-hidden="true">&middot;</span>
              <span>{post.views} views</span>
            </div>
          </div>
        </div>
      </div>
    </article>
  );
};

export default PostCard;