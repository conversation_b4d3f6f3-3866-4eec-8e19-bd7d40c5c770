import React, { useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useBlog } from '../context/BlogContext';
import PostCard from '../components/PostCard';

const CategoryPage: React.FC = () => {
  const { categoryName } = useParams<{ categoryName: string }>();
  const { posts } = useBlog();

  const decodedCategoryName = categoryName ? decodeURIComponent(categoryName) : '';

  const filteredPosts = useMemo(() => {
    return posts.filter(post => post.category === decodedCategoryName && post.status === 'published');
  }, [posts, decodedCategoryName]);

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16">
      <div className="border-b border-neutral-200 pb-6 mb-10">
        <h1 className="text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl font-display">
          Category: <span className="text-primary">{decodedCategoryName}</span>
        </h1>
        <p className="mt-3 text-lg text-neutral-500">
          Browsing all published articles in the "{decodedCategoryName}" category.
        </p>
      </div>

      {filteredPosts.length > 0 ? (
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {filteredPosts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 px-4 bg-white rounded-xl border border-neutral-200">
          <h3 className="text-xl font-medium text-neutral-900 font-display">No Posts in this Category</h3>
          <p className="mt-2 text-sm text-neutral-500">
            There are currently no published posts in this category.
          </p>
          <div className="mt-6">
            <Link
              to="/"
              className="inline-flex items-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              Back to Home
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryPage;