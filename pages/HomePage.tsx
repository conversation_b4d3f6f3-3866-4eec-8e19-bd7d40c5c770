import React, { useState, useMemo } from 'react';
import { useBlog } from '../context/BlogContext';
import PostCard from '../components/PostCard';
import { SearchIcon } from '../components/Icons';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  const { posts, getCategories, getTags } = useBlog();
  const [searchTerm, setSearchTerm] = useState('');

  const publishedPosts = useMemo(() => posts.filter(p => p.status === 'published'), [posts]);

  const filteredPosts = useMemo(() => {
    if (!searchTerm) return publishedPosts;
    return publishedPosts.filter(
      post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [searchTerm, publishedPosts]);

  const categories = useMemo(() => getCategories(), [posts]);
  const tags = useMemo(() => getTags(), [posts]);

  return (
    <div className="bg-neutral-50 min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold tracking-tight text-neutral-900 sm:text-6xl font-display">From the Blog</h1>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-neutral-600">
            Stay up to date with the latest articles and posts.
          </p>
        </div>

        <div className="mb-12 max-w-2xl mx-auto">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-5">
              <SearchIcon className="h-6 w-6 text-neutral-400" />
            </div>
            <input
              type="search"
              name="search"
              id="search"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="block w-full rounded-full border-neutral-300 pl-14 shadow-sm focus:border-primary-dark focus:ring-primary-dark sm:text-base py-4 bg-white text-neutral-900 placeholder-neutral-500"
              placeholder="Search articles by title, tag, or content..."
              aria-label="Search articles"
            />
          </div>
        </div>

        <div className="grid grid-cols-12 gap-x-8">
          <main className="col-span-12 lg:col-span-9">
            {filteredPosts.length > 0 ? (
              <div className="grid gap-10 md:grid-cols-2 xl:grid-cols-3">
                {filteredPosts.map(post => (
                  <PostCard key={post.id} post={post} />
                ))}
              </div>
            ) : (
              <div className="text-center py-24 px-6 bg-white rounded-2xl border border-neutral-200 shadow-sm">
                <h3 className="text-3xl font-bold text-neutral-900 font-display">No Posts Found</h3>
                <p className="mt-4 text-lg text-neutral-600">
                  Your search for "{searchTerm}" did not return any results.
                </p>
                <button 
                  onClick={() => setSearchTerm('')} 
                  className="mt-8 inline-flex items-center rounded-full border border-transparent bg-primary px-6 py-3 text-base font-semibold text-white shadow-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-neutral-50 transition-colors"
                >
                  Clear Search
                </button>
              </div>
            )}
          </main>
          <aside className="col-span-3 hidden lg:block">
            <div className="sticky top-28 space-y-10 p-6 bg-white rounded-2xl border border-neutral-200 shadow-sm">
              <div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-5 font-display">Categories</h3>
                <ul className="space-y-3">
                  {categories.map(category => (
                    <li key={category}>
                      <Link to={`/category/${encodeURIComponent(category)}`} className="text-neutral-600 hover:text-primary-dark transition-colors font-medium text-base">
                        {category}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-5 font-display">Tags</h3>
                <div className="flex flex-wrap gap-3">
                  {tags.map(tag => (
                    <Link key={tag} to={`/tag/${encodeURIComponent(tag)}`} className="inline-flex items-center rounded-full bg-neutral-100 px-4 py-2 text-sm font-semibold text-neutral-700 hover:bg-neutral-200 hover:text-neutral-900 transition-colors">
                      #{tag}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
};

export default HomePage;