import React, { useState } from 'react';
import { useBlog } from '../context/BlogContext';
import AdminLayout from '../components/AdminLayout';

const AdminPage: React.FC = () => {
  const { isLoggedIn, login } = useBlog();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await login(email, password);
    if (!success) {
      setError('Incorrect password.');
    } else {
      setError('');
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="flex min-h-[70vh] items-center justify-center bg-neutral-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8 bg-white p-8 md:p-12 rounded-xl shadow-lg border border-neutral-200">
          <div>
            <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-neutral-900 font-display">Admin Login</h2>
          </div>
          <form className="mt-8 space-y-6" onSubmit={handleLogin}>
            <div>
              <label htmlFor="email-address" className="sr-only">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={e => setEmail(e.target.value)}
                className="relative block w-full rounded-t-md border-neutral-300 px-3 py-3 text-neutral-900 placeholder-neutral-500 focus:z-10 focus:border-primary focus:outline-none focus:ring-primary sm:text-sm shadow-sm"
                placeholder="Email address"
              />
            </div>
            <div>
              <label htmlFor="password-admin" className="sr-only">Password</label>
              <input
                id="password-admin"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={e => setPassword(e.target.value)}
                className="relative block w-full rounded-b-md border-neutral-300 px-3 py-3 text-neutral-900 placeholder-neutral-500 focus:z-10 focus:border-primary focus:outline-none focus:ring-primary sm:text-sm shadow-sm"
                placeholder="Password"
              />
            </div>
            {error && <p className="text-sm text-red-600">{error}</p>}
            <div>
              <button
                type="submit"
                className="group relative flex w-full justify-center rounded-md border border-transparent bg-primary py-2 px-4 text-sm font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
              >
                Sign in
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <h1 className="text-3xl font-bold tracking-tight text-neutral-900 font-display">Dashboard</h1>
      <p className="mt-4 text-neutral-600">
        Welcome to your blog's dashboard. Here you can manage your posts, media, and settings.
      </p>
    </AdminLayout>
  );
};

export default AdminPage;