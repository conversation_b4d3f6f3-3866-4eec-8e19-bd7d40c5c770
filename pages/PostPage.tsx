import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import { useBlog } from '../context/BlogContext';

const PostPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { getPostBySlug } = useBlog();
  
  const post = slug ? getPostBySlug(slug) : undefined;

  useEffect(() => {
    if (post) {
      document.title = `${post.seoTitle} | Gemini Blog`;
      window.scrollTo(0, 0);
    }
    // Cleanup function to reset title
    return () => {
      document.title = 'Gemini Blog';
    };
  }, [post]);

  if (!post) {
    return <Navigate to="/404" replace />;
  }

  return (
    <div className="bg-white py-16 sm:py-24">
      <div className="mx-auto max-w-3xl px-6 lg:px-8">
        <header className="mb-10">
          <div className="mb-4">
             <Link
              to={`/category/${encodeURIComponent(post.category)}`}
              className="text-sm font-semibold uppercase tracking-wider text-primary bg-primary/10 px-2.5 py-1 rounded-md hover:bg-primary/20 transition-colors"
            >
              {post.category}
            </Link>
          </div>
          <h1 className="mt-2 text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl font-display">{post.title}</h1>
          <p className="mt-6 text-xl leading-8 text-neutral-600">{post.seoDescription}</p>
        
          <div className="mt-8 flex items-center gap-x-4">
              <img src={post.authorImageUrl} alt={post.author} className="h-12 w-12 rounded-full bg-neutral-50 object-cover" />
              <div className="text-sm leading-6">
                <p className="font-semibold text-neutral-900">{post.author}</p>
                <div className="text-neutral-600 flex items-center space-x-2">
                  <span>{new Date(post.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                  <span>&middot;</span>
                  <span>{post.views} views</span>
                </div>
              </div>
          </div>
        </header>

        <figure className="mb-12">
          <img className="aspect-video rounded-xl bg-neutral-100 object-cover w-full" src={post.imageUrl} alt={post.title} />
        </figure>

        <div className="prose prose-lg max-w-none prose-neutral prose-headings:font-display prose-headings:text-neutral-900 prose-a:text-primary hover:prose-a:text-primary-dark" style={{ whiteSpace: 'pre-wrap' }}>
          {post.content}
        </div>

        {post.tags && post.tags.length > 0 && (
          <div className="mt-16 border-t border-neutral-200 pt-8">
             <h3 className="text-lg font-bold text-neutral-900 mb-4 font-display">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map(tag => (
                <Link
                  key={tag}
                  to={`/tag/${encodeURIComponent(tag)}`}
                  className="inline-flex items-center rounded-full bg-neutral-100 px-3 py-1 text-sm font-medium text-neutral-600 hover:bg-neutral-200 hover:text-neutral-800 transition-colors"
                >
                  #{tag}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostPage;