import React, { useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useBlog } from '../context/BlogContext';
import PostCard from '../components/PostCard';

const TagPage: React.FC = () => {
  const { tagName } = useParams<{ tagName: string }>();
  const { posts } = useBlog();

  const decodedTagName = tagName ? decodeURIComponent(tagName) : '';

  const filteredPosts = useMemo(() => {
    return posts.filter(post => post.tags.includes(decodedTagName) && post.status === 'published');
  }, [posts, decodedTagName]);

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16">
      <div className="border-b border-neutral-200 pb-6 mb-10">
        <h1 className="text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl font-display">
          Tagged: <span className="text-primary">#{decodedTagName}</span>
        </h1>
        <p className="mt-3 text-lg text-neutral-500">
          Browsing all published articles tagged with "{decodedTagName}".
        </p>
      </div>

      {filteredPosts.length > 0 ? (
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {filteredPosts.map(post => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 px-4 bg-white rounded-xl border border-neutral-200">
          <h3 className="text-xl font-medium text-neutral-900 font-display">No Posts Found</h3>
          <p className="mt-2 text-sm text-neutral-500">
            There are currently no published posts with this tag.
          </p>
          <div className="mt-6">
            <Link
              to="/"
              className="inline-flex items-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              Back to Home
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default TagPage;