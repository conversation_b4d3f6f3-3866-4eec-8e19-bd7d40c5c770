import React, { useState } from 'react';
import { useBlog } from '../context/BlogContext';
import { Post } from '../types';
import PostForm from '../components/PostForm';
import { EditIcon, TrashIcon, PlusCircleIcon } from '../components/Icons';
import AdminLayout from '../components/AdminLayout';

const PostsPage: React.FC = () => {
  const { posts, deletePost } = useBlog();
  const [showForm, setShowForm] = useState(false);
  const [postToEdit, setPostToEdit] = useState<Post | null>(null);

  const handleEdit = (post: Post) => {
    setPostToEdit(post);
    setShowForm(true);
  };

  const handleCreateNew = () => {
    setPostToEdit(null);
    setShowForm(true);
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      deletePost(id);
    }
  };

  return (
    <AdminLayout>
      <div className="sm:flex sm:items-center sm:justify-between mb-10">
        <h1 className="text-3xl font-bold tracking-tight text-neutral-900 font-display">Posts</h1>
        <div className="mt-4 sm:mt-0">
          <button
            type="button"
            onClick={handleCreateNew}
            className="inline-flex items-center gap-x-2 rounded-md bg-primary px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-dark focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary transition-colors"
          >
            <PlusCircleIcon className="-ml-0.5 h-5 w-5" />
            Create New Post
          </button>
        </div>
      </div>

      <div className="flow-root">
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table className="min-w-full divide-y divide-neutral-300">
                <thead className="bg-neutral-50">
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-neutral-900 sm:pl-6">Title</th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-900">Status</th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-900">Category</th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-900">Date</th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6"><span className="sr-only">Actions</span></th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-neutral-200 bg-white">
                  {posts.map((post) => (
                    <tr key={post.id} className="hover:bg-neutral-50 transition-colors">
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-neutral-900 sm:pl-6">{post.title}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">
                        <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${post.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                          {post.status}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">{post.category}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-500">{new Date(post.createdAt).toLocaleDateString()}</td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <button onClick={() => handleEdit(post)} className="text-primary hover:text-primary-dark p-1 transition-colors"><EditIcon className="h-5 w-5" /><span className="sr-only">, {post.title}</span></button>
                        <button onClick={() => handleDelete(post.id)} className="text-red-600 hover:text-red-800 p-1 ml-2 transition-colors"><TrashIcon className="h-5 w-5" /><span className="sr-only">, {post.title}</span></button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {showForm && <PostForm postToEdit={postToEdit} onClose={() => setShowForm(false)} />}
    </AdminLayout>
  );
};

export default PostsPage;